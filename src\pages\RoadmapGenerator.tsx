import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { integrationBridgeService } from '../services/integrationBridgeService'
import { progressiveLearningTracker } from '../services/progressiveLearningTracker'
import type { UserPreferences } from '../services/aiService'

interface FormData {
  currentLevel: string
  learningGoal: string
  timeCommitment: string
  focusAreas: string[]
}

const RoadmapGenerator = () => {
  const navigate = useNavigate()
  const [formData, setFormData] = useState<FormData>({
    currentLevel: '',
    learningGoal: '',
    timeCommitment: '',
    focusAreas: []
  })
  const [isGenerating, setIsGenerating] = useState(false)

  const handleFocusAreaToggle = (area: string) => {
    setFormData(prev => ({
      ...prev,
      focusAreas: prev.focusAreas.includes(area)
        ? prev.focusAreas.filter(a => a !== area)
        : [...prev.focusAreas, area]
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsGenerating(true)
    
    try {
      // Generate progressive roadmap using new pedagogically-sound approach
      const userId = `user_${Date.now()}`; // Generate unique user ID
      const progressiveRoadmapResponse = await integrationBridgeService.generateProgressiveRoadmap(formData as UserPreferences, userId)
      
      // Modules already have proper status from progressive generation
      const modulesWithStatus = progressiveRoadmapResponse.modules.map(module => ({
        ...module,
        status: (module as any).status || 'available' as const,
        completionPercentage: (module as any).completionPercentage || 0
      }))

      // Create learning pathway with progressive tracking
      const learningPathway = progressiveLearningTracker.createLearningPathway(
        formData as UserPreferences,
        modulesWithStatus
      )

      // Store progressive roadmap data
      localStorage.setItem('roadmapData', JSON.stringify(formData))
      localStorage.setItem('aiRoadmapResponse', JSON.stringify(progressiveRoadmapResponse))
      localStorage.setItem('learningPathway', JSON.stringify(learningPathway))
      localStorage.setItem('userId', userId) // Store user ID for progressive generation
      
      setIsGenerating(false)
      navigate('/roadmap')
    } catch (error) {
      console.error('Failed to generate progressive roadmap:', error)
      setIsGenerating(false)
      // You could add error handling UI here
      alert('Failed to generate roadmap. Please try again.')
    }
  }

  if (isGenerating) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="relative w-32 h-32 mx-auto mb-8">
            <div className="absolute inset-0 border-4 border-red-600 rounded-full animate-spin border-t-transparent"></div>
            <div className="absolute inset-2 border-4 border-yellow-600 rounded-full animate-spin border-b-transparent" style={{ animationDirection: 'reverse' }}></div>
            <div className="absolute inset-0 flex items-center justify-center text-2xl">道</div>
          </div>
          <h2 className="text-2xl font-semibold text-red-400 mb-4">Crafting Your Enhanced Learning Journey</h2>
          <p className="text-gray-400">Using our new fast AI system to create your personalized learning roadmap with detailed lesson plans...</p>
          <div className="mt-4 p-3 bg-green-900/30 border border-green-700 rounded-lg max-w-md mx-auto">
            <p className="text-green-300 text-sm">
              ⚡ <strong>Performance Update:</strong> Now using split AI generation for much faster roadmap creation!
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <Link to="/" className="text-red-400 hover:text-red-300 transition-colors">
            ← Back to Home
          </Link>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-6 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4 text-red-500">Create Your Learning Roadmap</h1>
          <p className="text-xl text-gray-400">Tell us about your goals and we'll craft a personalized journey</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Current Level */}
          <div className="bg-gray-800 p-8 border-l-4 border-red-600">
            <h3 className="text-xl font-semibold mb-6 text-red-400">What's your current Japanese level?</h3>
            <div className="grid md:grid-cols-2 gap-4">
              {[
                { value: 'absolute-beginner', label: 'Absolute Beginner', desc: 'No prior knowledge' },
                { value: 'beginner', label: 'Beginner', desc: 'Know some hiragana/katakana' },
                { value: 'elementary', label: 'Elementary', desc: 'Basic grammar and vocabulary' },
                { value: 'intermediate', label: 'Intermediate', desc: 'Can hold simple conversations' },
                { value: 'advanced', label: 'Advanced', desc: 'Fluent in most situations' }
              ].map((level) => (
                <label key={level.value} className="cursor-pointer">
                  <input
                    type="radio"
                    name="currentLevel"
                    value={level.value}
                    checked={formData.currentLevel === level.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, currentLevel: e.target.value }))}
                    className="sr-only"
                  />
                  <div className={`p-4 border-2 transition-all duration-200 ${
                    formData.currentLevel === level.value
                      ? 'border-red-500 bg-gray-700'
                      : 'border-gray-600 bg-gray-700 hover:border-gray-500'
                  }`}>
                    <div className="font-semibold text-gray-100">{level.label}</div>
                    <div className="text-sm text-gray-400">{level.desc}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Learning Goal */}
          <div className="bg-gray-800 p-8 border-l-4 border-red-600">
            <h3 className="text-xl font-semibold mb-6 text-red-400">What's your primary learning goal?</h3>
            <div className="space-y-3">
              {[
                { value: 'conversation', label: 'Conversational Fluency', desc: 'Speak naturally in daily situations' },
                { value: 'business', label: 'Business Japanese', desc: 'Professional communication skills' },
                { value: 'academic', label: 'Academic Study', desc: 'Prepare for JLPT or university' },
                { value: 'travel', label: 'Travel & Culture', desc: 'Navigate Japan and understand culture' },
                { value: 'anime-manga', label: 'Anime & Manga', desc: 'Understand Japanese media without subtitles' }
              ].map((goal) => (
                <label key={goal.value} className="cursor-pointer block">
                  <input
                    type="radio"
                    name="learningGoal"
                    value={goal.value}
                    checked={formData.learningGoal === goal.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, learningGoal: e.target.value }))}
                    className="sr-only"
                  />
                  <div className={`p-4 border-2 transition-all duration-200 ${
                    formData.learningGoal === goal.value
                      ? 'border-red-500 bg-gray-700'
                      : 'border-gray-600 bg-gray-700 hover:border-gray-500'
                  }`}>
                    <div className="font-semibold text-gray-100">{goal.label}</div>
                    <div className="text-sm text-gray-400">{goal.desc}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Time Commitment */}
          <div className="bg-gray-800 p-8 border-l-4 border-red-600">
            <h3 className="text-xl font-semibold mb-6 text-red-400">How much time can you dedicate daily?</h3>
            <div className="grid md:grid-cols-4 gap-4">
              {[
                { value: '15-min', label: '15 minutes', desc: 'Quick daily practice' },
                { value: '30-min', label: '30 minutes', desc: 'Steady progress' },
                { value: '1-hour', label: '1 hour', desc: 'Focused learning' },
                { value: '2-hours', label: '2+ hours', desc: 'Intensive study' }
              ].map((time) => (
                <label key={time.value} className="cursor-pointer">
                  <input
                    type="radio"
                    name="timeCommitment"
                    value={time.value}
                    checked={formData.timeCommitment === time.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, timeCommitment: e.target.value }))}
                    className="sr-only"
                  />
                  <div className={`p-4 border-2 text-center transition-all duration-200 ${
                    formData.timeCommitment === time.value
                      ? 'border-red-500 bg-gray-700'
                      : 'border-gray-600 bg-gray-700 hover:border-gray-500'
                  }`}>
                    <div className="font-semibold text-gray-100">{time.label}</div>
                    <div className="text-sm text-gray-400">{time.desc}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Focus Areas */}
          <div className="bg-gray-800 p-8 border-l-4 border-red-600">
            <h3 className="text-xl font-semibold mb-6 text-red-400">Which areas would you like to focus on? (Select multiple)</h3>
            <div className="grid md:grid-cols-3 gap-4">
              {[
                { value: 'speaking', label: 'Speaking', icon: '🗣️' },
                { value: 'listening', label: 'Listening', icon: '👂' },
                { value: 'reading', label: 'Reading', icon: '📖' },
                { value: 'writing', label: 'Writing', icon: '✍️' },
                { value: 'kanji', label: 'Kanji', icon: '漢' },
                { value: 'grammar', label: 'Grammar', icon: '📝' }
              ].map((area) => (
                <label key={area.value} className="cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.focusAreas.includes(area.value)}
                    onChange={() => handleFocusAreaToggle(area.value)}
                    className="sr-only"
                  />
                  <div className={`p-4 border-2 text-center transition-all duration-200 ${
                    formData.focusAreas.includes(area.value)
                      ? 'border-red-500 bg-gray-700'
                      : 'border-gray-600 bg-gray-700 hover:border-gray-500'
                  }`}>
                    <div className="text-2xl mb-2">{area.icon}</div>
                    <div className="font-semibold text-gray-100">{area.label}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Submit Button */}
          <div className="text-center pt-8">
            <button
              type="submit"
              disabled={!formData.currentLevel || !formData.learningGoal || !formData.timeCommitment || formData.focusAreas.length === 0}
              className="px-12 py-4 bg-gradient-to-r from-red-700 to-red-600 hover:from-red-600 hover:to-red-500 disabled:from-gray-700 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-semibold text-lg transition-all duration-300 border border-yellow-600 hover:border-yellow-500 disabled:border-gray-600"
              style={{ clipPath: 'polygon(10px 0%, 100% 0%, calc(100% - 10px) 100%, 0% 100%)' }}
            >
              Generate My Roadmap
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default RoadmapGenerator