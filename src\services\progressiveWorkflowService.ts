/**
 * Progressive Workflow Service
 * 
 * Orchestrates the gradual roadmap generation process, managing when and how
 * to generate new modules based on user progress and pedagogical principles.
 */

import { aiLogger } from '../utils/aiLogger';
import { progressiveRoadmapService } from './progressiveRoadmapService';
import { splitAIService } from './splitAIService';
import type { UserPreferences } from './aiService';
import type { RoadmapOutline, DetailedModule, LearningProgress } from './progressiveRoadmapService';

// Re-export LearningProgress for other components
export type { LearningProgress };

export interface ProgressiveRoadmap {
  outline: RoadmapOutline;
  generatedModules: Map<string, DetailedModule>;
  currentPhase: string;
  nextPhaseToGenerate: string | null;
  userProgress: LearningProgress;
  generationHistory: GenerationEvent[];
}

export interface GenerationEvent {
  timestamp: string;
  type: 'outline' | 'module' | 'lesson';
  phaseId?: string;
  moduleId?: string;
  lessonId?: string;
  trigger: 'initial' | 'progress' | 'user-request' | 'adaptive';
  adaptations: string[];
}

export interface ModuleUnlockCondition {
  type: 'completion' | 'mastery' | 'time' | 'assessment';
  target: string;
  threshold: number;
  description: string;
}

class ProgressiveWorkflowService {
  private roadmaps = new Map<string, ProgressiveRoadmap>();

  /**
   * Initialize a new progressive roadmap for a user
   */
  async initializeProgressiveRoadmap(
    userId: string,
    preferences: UserPreferences
  ): Promise<ProgressiveRoadmap> {
    const operationId = 'initializeProgressiveRoadmap';
    
    try {
      aiLogger.info('ProgressiveWorkflowService', operationId, 'Initializing progressive roadmap', {
        userId,
        userLevel: preferences.currentLevel,
        learningGoal: preferences.learningGoal
      });

      // Step 1: Generate high-level roadmap outline
      const outline = await progressiveRoadmapService.generateRoadmapOutline(preferences);
      
      // Step 2: Initialize user progress
      const initialProgress: LearningProgress = {
        completedModules: [],
        completedLessons: [],
        currentModule: null,
        skillMastery: {},
        learningVelocity: this.estimateInitialVelocity(preferences),
        difficultyPreference: 'standard',
        strugglingAreas: [],
        strongAreas: [],
        timeSpentPerLesson: this.estimateInitialTimePerLesson(preferences),
        completionRate: 0,
        lastActiveDate: new Date().toISOString()
      };

      // Step 3: Generate first module immediately
      const firstModule = await progressiveRoadmapService.generateDetailedModule(
        outline.currentPhase.phaseId,
        preferences,
        initialProgress,
        outline
      );

      // Step 4: Create progressive roadmap
      const progressiveRoadmap: ProgressiveRoadmap = {
        outline,
        generatedModules: new Map([[outline.currentPhase.phaseId, firstModule]]),
        currentPhase: outline.currentPhase.phaseId,
        nextPhaseToGenerate: outline.upcomingPhases[0]?.phaseId || null,
        userProgress: initialProgress,
        generationHistory: [
          {
            timestamp: new Date().toISOString(),
            type: 'outline',
            trigger: 'initial',
            adaptations: ['Initial roadmap structure']
          },
          {
            timestamp: new Date().toISOString(),
            type: 'module',
            phaseId: outline.currentPhase.phaseId,
            moduleId: firstModule.id,
            trigger: 'initial',
            adaptations: ['First module generation']
          }
        ]
      };

      this.roadmaps.set(userId, progressiveRoadmap);

      aiLogger.info('ProgressiveWorkflowService', operationId, 'Progressive roadmap initialized', {
        userId,
        totalEstimatedModules: outline.learningPath.totalEstimatedModules,
        firstModuleId: firstModule.id,
        firstModuleLessons: firstModule.lessons
      });

      return progressiveRoadmap;

    } catch (error) {
      aiLogger.error('ProgressiveWorkflowService', operationId, 'Failed to initialize progressive roadmap', {
        userId,
        error: error
      });
      throw error;
    }
  }

  /**
   * Check if next module should be generated based on user progress
   */
  async checkAndGenerateNextModule(
    userId: string,
    currentProgress: LearningProgress
  ): Promise<DetailedModule | null> {
    const operationId = 'checkAndGenerateNextModule';
    const roadmap = this.roadmaps.get(userId);
    
    if (!roadmap) {
      aiLogger.warn('ProgressiveWorkflowService', operationId, 'No roadmap found for user', { userId });
      return null;
    }

    try {
      // Update user progress
      roadmap.userProgress = currentProgress;

      // Check if next module should be generated
      const shouldGenerate = this.shouldGenerateNextModule(roadmap);
      
      if (!shouldGenerate.generate) {
        aiLogger.debug('ProgressiveWorkflowService', operationId, 'Next module generation not needed', {
          userId,
          reason: shouldGenerate.reason,
          nextPhase: roadmap.nextPhaseToGenerate
        });
        return null;
      }

      if (!roadmap.nextPhaseToGenerate) {
        aiLogger.info('ProgressiveWorkflowService', operationId, 'No more phases to generate', { userId });
        return null;
      }

      aiLogger.info('ProgressiveWorkflowService', operationId, 'Generating next module', {
        userId,
        nextPhase: roadmap.nextPhaseToGenerate,
        trigger: shouldGenerate.trigger
      });

      // Generate next module with adaptive elements
      const nextModule = await progressiveRoadmapService.generateDetailedModule(
        roadmap.nextPhaseToGenerate,
        this.getUserPreferencesFromRoadmap(roadmap),
        currentProgress,
        roadmap.outline
      );

      // Update roadmap
      roadmap.generatedModules.set(roadmap.nextPhaseToGenerate, nextModule);
      roadmap.currentPhase = roadmap.nextPhaseToGenerate;
      
      // Find next phase to generate
      const currentPhaseIndex = roadmap.outline.upcomingPhases.findIndex(
        p => p.phaseId === roadmap.nextPhaseToGenerate
      );
      roadmap.nextPhaseToGenerate = roadmap.outline.upcomingPhases[currentPhaseIndex + 1]?.phaseId || null;

      // Record generation event
      roadmap.generationHistory.push({
        timestamp: new Date().toISOString(),
        type: 'module',
        phaseId: roadmap.currentPhase,
        moduleId: nextModule.id,
        trigger: shouldGenerate.trigger,
        adaptations: this.identifyAdaptations(currentProgress, nextModule)
      });

      aiLogger.info('ProgressiveWorkflowService', operationId, 'Next module generated successfully', {
        userId,
        moduleId: nextModule.id,
        moduleLessons: nextModule.lessons,
        adaptations: roadmap.generationHistory[roadmap.generationHistory.length - 1].adaptations
      });

      return nextModule;

    } catch (error) {
      aiLogger.error('ProgressiveWorkflowService', operationId, 'Failed to generate next module', {
        userId,
        error: error
      });
      throw error;
    }
  }

  /**
   * Generate lesson content on-demand for a specific lesson
   */
  async generateLessonContent(
    userId: string,
    moduleId: string,
    lessonId: string
  ): Promise<any> {
    const operationId = 'generateLessonContent';
    const roadmap = this.roadmaps.get(userId);
    
    if (!roadmap) {
      throw new Error(`No roadmap found for user ${userId}`);
    }

    try {
      // Find the module and lesson plan
      const module = Array.from(roadmap.generatedModules.values()).find(m => m.id === moduleId);
      if (!module) {
        throw new Error(`Module ${moduleId} not found`);
      }

      const lessonPlan = module.lessonPlans.find(l => l.id === lessonId);
      if (!lessonPlan) {
        throw new Error(`Lesson ${lessonId} not found in module ${moduleId}`);
      }

      aiLogger.info('ProgressiveWorkflowService', operationId, 'Generating lesson content with progress adaptation', {
        userId,
        moduleId,
        lessonId,
        userProgress: roadmap.userProgress
      });

      // Generate lesson content using on-demand service
      const { onDemandLessonService } = await import('./onDemandLessonService');

      const moduleContext = {
        moduleId: module.id,
        moduleTitle: module.title,
        moduleDescription: module.description,
        contentContext: module.contentContext
      };

      const userProgress = {
        completedLessons: roadmap.userProgress.completedLessons,
        skillMastery: roadmap.userProgress.skillMastery,
        strugglingAreas: roadmap.userProgress.strugglingAreas,
        strongAreas: roadmap.userProgress.strongAreas,
        learningVelocity: roadmap.userProgress.learningVelocity,
        difficultyPreference: roadmap.userProgress.difficultyPreference
      };

      const previousLessons = module.lessonPlans.slice(0, module.lessonPlans.findIndex(l => l.id === lessonId));

      const fullLesson = await onDemandLessonService.generateLessonContent(
        lessonPlan,
        moduleContext,
        userProgress,
        previousLessons
      );

      // Record generation event
      roadmap.generationHistory.push({
        timestamp: new Date().toISOString(),
        type: 'lesson',
        phaseId: roadmap.currentPhase,
        moduleId,
        lessonId,
        trigger: 'user-request',
        adaptations: ['On-demand lesson content generation']
      });

      aiLogger.info('ProgressiveWorkflowService', operationId, 'Lesson content generated successfully', {
        userId,
        moduleId,
        lessonId,
        exerciseCount: fullLesson.content.exercises.length
      });

      return fullLesson;

    } catch (error) {
      aiLogger.error('ProgressiveWorkflowService', operationId, 'Failed to generate lesson content', {
        userId,
        moduleId,
        lessonId,
        error: error
      });
      throw error;
    }
  }

  /**
   * Get current roadmap state for a user
   */
  getProgressiveRoadmap(userId: string): ProgressiveRoadmap | null {
    return this.roadmaps.get(userId) || null;
  }

  /**
   * Update user progress and trigger adaptive adjustments
   */
  async updateUserProgress(
    userId: string,
    progressUpdate: Partial<LearningProgress>
  ): Promise<void> {
    const roadmap = this.roadmaps.get(userId);
    if (!roadmap) return;

    // Update progress
    Object.assign(roadmap.userProgress, progressUpdate);

    // Check if adaptive adjustments are needed
    const adaptations = this.analyzeAdaptationNeeds(roadmap.userProgress);
    
    if (adaptations.length > 0) {
      aiLogger.info('ProgressiveWorkflowService', 'updateUserProgress', 'Adaptive adjustments identified', {
        userId,
        adaptations
      });

      // Record adaptation event
      roadmap.generationHistory.push({
        timestamp: new Date().toISOString(),
        type: 'module',
        trigger: 'adaptive',
        adaptations
      });
    }
  }

  private shouldGenerateNextModule(roadmap: ProgressiveRoadmap): { generate: boolean; reason: string; trigger: 'progress' | 'user-request' | 'adaptive' } {
    const currentModule = roadmap.generatedModules.get(roadmap.currentPhase);
    if (!currentModule) {
      return { generate: false, reason: 'No current module found', trigger: 'progress' };
    }

    // Check if user is approaching completion of current module
    const completionThreshold = 0.7; // Generate next when 70% through current
    if (roadmap.userProgress.completionRate >= completionThreshold) {
      return { generate: true, reason: 'User approaching module completion', trigger: 'progress' };
    }

    // Check if user is progressing faster than expected
    if (roadmap.userProgress.learningVelocity > 1.5) {
      return { generate: true, reason: 'Fast learner - prepare next module', trigger: 'adaptive' };
    }

    return { generate: false, reason: 'Conditions not met for next module generation', trigger: 'progress' };
  }

  private estimateInitialVelocity(preferences: UserPreferences): number {
    // Estimate lessons per day based on time commitment
    if (preferences.timeCommitment.includes('2+ hours')) return 2.0;
    if (preferences.timeCommitment.includes('1-2 hours')) return 1.5;
    if (preferences.timeCommitment.includes('30-60 minutes')) return 1.0;
    return 0.5;
  }

  private estimateInitialTimePerLesson(preferences: UserPreferences): number {
    // Estimate minutes per lesson based on time commitment and learning style
    if (preferences.timeCommitment.includes('15-30 minutes')) return 20;
    if (preferences.timeCommitment.includes('30-60 minutes')) return 30;
    if (preferences.timeCommitment.includes('1-2 hours')) return 45;
    return 60;
  }

  private getUserPreferencesFromRoadmap(roadmap: ProgressiveRoadmap): UserPreferences {
    return {
      currentLevel: roadmap.outline.metadata.userLevel,
      learningGoal: roadmap.outline.metadata.learningGoal,
      timeCommitment: '30-60 minutes daily', // Default, should be stored
      focusAreas: ['reading', 'grammar'], // Default, should be stored
      learningStyle: 'visual' // Default, should be stored
    };
  }

  private identifyAdaptations(progress: LearningProgress, module: DetailedModule): string[] {
    const adaptations: string[] = [];
    
    if (progress.strugglingAreas.length > 0) {
      adaptations.push(`Reinforcement for: ${progress.strugglingAreas.join(', ')}`);
    }
    
    if (progress.difficultyPreference !== 'standard') {
      adaptations.push(`Difficulty adjusted to: ${progress.difficultyPreference}`);
    }
    
    if (progress.learningVelocity > 1.5) {
      adaptations.push('Accelerated pace for fast learner');
    } else if (progress.learningVelocity < 0.5) {
      adaptations.push('Slower pace with more practice');
    }
    
    return adaptations;
  }

  private analyzeAdaptationNeeds(progress: LearningProgress): string[] {
    const adaptations: string[] = [];
    
    // Check for struggling areas
    if (progress.strugglingAreas.length > 2) {
      adaptations.push('Increase reinforcement activities');
    }
    
    // Check completion rate
    if (progress.completionRate < 0.6) {
      adaptations.push('Reduce lesson complexity');
    } else if (progress.completionRate > 0.95) {
      adaptations.push('Increase challenge level');
    }
    
    // Check time spent
    if (progress.timeSpentPerLesson > 45) {
      adaptations.push('Simplify lesson structure');
    }
    
    return adaptations;
  }
}

export const progressiveWorkflowService = new ProgressiveWorkflowService();
